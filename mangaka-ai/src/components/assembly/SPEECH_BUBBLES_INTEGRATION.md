# 🎨 Speech Bubbles TipTap-first - Intégration Polotno

## 📋 Vue d'ensemble

Cette intégration ajoute un système de Speech Bubbles intelligent basé sur TipTap dans le menu Assemblage existant, en respectant parfaitement l'architecture Polotno et la cohérence avec le Tool Panel.

## 🏗️ Architecture

### **1. TipTap-first Design**
- **Cœur** : TipTap Editor pour l'édition de texte native
- **Wrapper** : CSS/SVG pour les formes de bulles (speech, thought, explosion)
- **Queue 360°** : Système de manipulation directionnelle avec handles

### **2. Trois Modes UX**
- **Mode Lecture** (`read`) : TipTap readonly, bulle stylisée, interaction minimale
- **Mode Édition** (`edit`) : TipTap editable, manipulation désactivée, focus automatique
- **Mode Manipulation** (`manip`) : TipTap readonly, handles actifs pour resize/déplacement

### **3. Intégration Polotno Cohérente**
- **PolotnoContext étendu** : Actions Speech Bubble intégrées
- **SimpleCanvasEditor** : SpeechBubbleContainer ajouté
- **PolotnoVerticalToolbar** : Utilise le système existant
- **Types unifiés** : Compatible avec assembly.types.ts

## 📁 Structure des Fichiers

```
src/components/assembly/
├── ui/
│   ├── TipTapSpeechBubble.tsx          # Composant principal
│   ├── TipTapSpeechBubble.css          # Styles dédiés
│   ├── SpeechBubbleSelectionFrame.tsx  # Cadres de sélection
│   └── SpeechBubbleContainer.tsx       # Conteneur intégrateur
├── core/
│   ├── SpeechBubbleManager.ts          # Gestionnaire centralisé
│   └── SimpleCanvasEditor.tsx          # Intégration canvas
├── context/
│   └── PolotnoContext.tsx              # Actions étendues
├── types/
│   └── polotno.types.ts                # Types étendus
└── test/
    └── SpeechBubbleTest.tsx            # Composant de test
```

## 🔧 Composants Principaux

### **TipTapSpeechBubble**
```typescript
interface TipTapSpeechBubbleProps {
  element: DialogueElement
  isSelected: boolean
  mode: BubbleMode
  onUpdate: (elementId: string, updates: Partial<DialogueElement>) => void
  onSelect: (elementId: string | null) => void
  onModeChange: (mode: BubbleMode) => void
  onResize?: (elementId: string, newTransform: Partial<DialogueElement['transform']>) => void
  onRotate?: (elementId: string, rotation: number) => void
  onTailDrag?: (elementId: string, tailDirection: number) => void
}
```

### **SpeechBubbleManager**
- Gestion centralisée des modes et états
- Intégration avec le système de sélection existant
- Callbacks vers PolotnoContext

### **SpeechBubbleContainer**
- Hook `useSpeechBubbleContainer()` pour intégration CanvasContext
- Fonction `createNewSpeechBubble()` pour création
- Gestion des événements clavier et souris

## 🎯 Intégration avec l'Existant

### **PolotnoContext Extended**
```typescript
// Nouveaux types
export type SpeechBubbleMode = 'read' | 'edit' | 'manip'

// Nouvel état
speechBubbles: Map<string, SpeechBubbleMode>
currentEditingBubble: string | null

// Nouvelles actions
createSpeechBubble: (x: number, y: number, bubbleType: BubbleType) => void
updateSpeechBubble: (elementId: string, updates: any) => void
setSpeechBubbleMode: (elementId: string, mode: SpeechBubbleMode) => void
startSpeechBubbleEdit: (elementId: string) => void
finishSpeechBubbleEdit: (elementId: string) => void
```

### **SimpleCanvasEditor Integration**
```typescript
// Nouvel état
const [speechBubbles, setSpeechBubbles] = useState<DialogueElement[]>([])
const [selectedSpeechBubbleIds, setSelectedSpeechBubbleIds] = useState<string[]>([])

// Gestionnaires intégrés
const handleSpeechBubbleUpdate = useCallback(...)
const createNewSpeechBubbleAtPosition = useCallback(...)

// Rendu intégré
<SpeechBubbleContainer
  elements={speechBubbles}
  selectedElementIds={selectedSpeechBubbleIds}
  onElementUpdate={handleSpeechBubbleUpdate}
  // ...
/>
```

## 🎨 Styles et UX

### **CSS Modulaire**
- `TipTapSpeechBubble.css` : Styles dédiés avec modes UX
- Transitions fluides entre modes
- Intégration TipTap transparente
- Responsive et accessible

### **Cohérence Visuelle**
- Cadres de sélection identiques aux panels
- Handles de manipulation uniformes
- Couleurs et styles cohérents avec le design system

## 🚀 Utilisation

### **Création de Bulle**
1. Cliquer sur l'outil Bulle dans PolotnoVerticalToolbar
2. Sélectionner le type dans BubbleTypeModal
3. Cliquer sur le canvas pour placer
4. Speech Bubble créée en mode 'manip'

### **Édition de Texte**
1. Double-cliquer sur une bulle → Mode 'edit'
2. TipTap s'active avec focus automatique
3. Édition native avec raccourcis clavier
4. Clic ailleurs ou Échap → Retour mode 'read'

### **Manipulation**
1. Clic simple → Mode 'manip'
2. Handles blancs : Redimensionnement
3. Handle orange : Direction de la queue
4. Drag : Déplacement

## 🧪 Tests

### **Composant de Test**
```typescript
import SpeechBubbleTest from '../test/SpeechBubbleTest'

// Utilisation
<SpeechBubbleTest />
```

### **Tests d'Intégration**
- Création via PolotnoVerticalToolbar
- Sélection via CanvasContext
- Édition TipTap intégrée
- Manipulation avec handles

## 🔄 Workflow Complet

1. **Utilisateur clique sur outil Bulle** → `PolotnoVerticalToolbar`
2. **Modal de sélection type** → `BubbleTypeModal`
3. **Clic sur canvas** → `SimpleCanvasEditor.handleMouseDown`
4. **Création Speech Bubble** → `createNewSpeechBubbleAtPosition`
5. **Ajout au state** → `setSpeechBubbles`
6. **Rendu TipTap** → `SpeechBubbleContainer` → `TipTapSpeechBubble`
7. **Interaction utilisateur** → `SpeechBubbleManager`
8. **Mise à jour Polotno** → `PolotnoContext` actions

## 📝 Modèle de Données

```typescript
interface DialogueElement {
  type: 'dialogue'
  id: string
  layerType: 'dialogue'
  text: string // Contenu TipTap HTML
  transform: {
    x: number, y: number, width: number, height: number
    rotation: number, alpha: number, zIndex: number
  }
  dialogueStyle: {
    type: BubbleType // 'speech' | 'thought' | 'explosion'
    backgroundColor: number
    outlineColor: number
    textColor: number
    fontSize: number
    fontFamily: string
    textAlign: 'left' | 'center' | 'right'
    tailAngleDegrees: number // 0-360° pour queue directionnelle
    tailLength: number
    // ...
  }
  properties: {
    name: string
    locked: boolean
    visible: boolean
  }
}
```

## 🎯 Avantages de cette Architecture

1. **Cohérence Polotno** : Intégration parfaite avec l'existant
2. **TipTap-first** : Édition de texte native et performante
3. **Modulaire** : Composants réutilisables et maintenables
4. **UX Professionnelle** : Trois modes distincts et intuitifs
5. **Performance** : Pas de conflits PixiJS/DOM
6. **Extensible** : Facile d'ajouter de nouveaux types de bulles

## 🔮 Évolutions Futures

- Support de styles de texte avancés (gras, italique, couleurs)
- Templates de bulles prédéfinis
- Import/Export de bulles
- Animations et transitions
- Collaboration temps réel sur les bulles
