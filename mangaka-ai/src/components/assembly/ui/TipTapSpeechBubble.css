/* TipTapSpeechBubble - Styles pour le composant Speech Bubble TipTap-first */

/* ===== CONTENEUR PRINCIPAL ===== */
.speech-bubble-container {
  position: absolute;
  user-select: none;
  transition: all 0.2s ease;
}

/* ===== MODES UX ===== */

/* Mode Lecture - Interaction minimale */
.speech-bubble-mode-read {
  cursor: pointer;
}

.speech-bubble-mode-read .speech-bubble-content {
  pointer-events: none;
}

/* Mode Édition - Focus sur le texte */
.speech-bubble-mode-edit {
  cursor: text;
  z-index: 1000; /* Au-dessus des autres éléments pendant l'édition */
}

.speech-bubble-mode-edit .speech-bubble-content {
  pointer-events: auto;
}

/* Mode Manipulation - Handles actifs */
.speech-bubble-mode-manip {
  cursor: move;
}

.speech-bubble-mode-manip .speech-bubble-content {
  pointer-events: none;
}

/* ===== ÉTATS DE SÉLECTION ===== */

.speech-bubble-selected {
  outline: 2px solid #ef4444;
  outline-offset: 2px;
}

.speech-bubble-editing {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* ===== TYPES DE BULLES ===== */

/* Bulle de dialogue classique */
.speech-bubble-speech {
  /* Styles spécifiques aux bulles de dialogue */
}

/* Bulle de pensée */
.speech-bubble-thought {
  /* Styles spécifiques aux bulles de pensée */
}

/* Bulle d'explosion */
.speech-bubble-explosion {
  /* Styles spécifiques aux bulles d'explosion */
}

/* ===== SVG ET FORMES ===== */

.speech-bubble-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

/* ===== CONTENU TIPTAP ===== */

.speech-bubble-content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* ===== ÉDITEUR TIPTAP ===== */

.tiptap-speech-bubble-editor {
  outline: none !important;
  border: none !important;
  background: transparent !important;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: inherit;
  font-size: inherit;
  font-family: inherit;
  color: inherit;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.tiptap-speech-bubble-editor:focus {
  outline: none !important;
  box-shadow: none !important;
}

.tiptap-editor-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tiptap-editor-content .ProseMirror {
  outline: none !important;
  border: none !important;
  background: transparent !important;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: inherit;
  font-size: inherit;
  font-family: inherit;
  color: inherit;
  line-height: 1.4;
  padding: 0;
  margin: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.tiptap-editor-content .ProseMirror p {
  margin: 0;
  padding: 0;
  text-align: inherit;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* ===== PLACEHOLDER ===== */

.tiptap-editor-content .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: rgba(255, 255, 255, 0.4);
  pointer-events: none;
  height: 0;
}

/* ===== AFFICHAGE DU TEXTE (MODE LECTURE) ===== */

.bubble-text-display {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: inherit;
  font-size: inherit;
  font-family: inherit;
  color: inherit;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.bubble-text-display p {
  margin: 0;
  padding: 0;
  text-align: inherit;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* ===== TRANSITIONS ET ANIMATIONS ===== */

.speech-bubble-container {
  transition: outline 0.2s ease, box-shadow 0.2s ease, z-index 0s;
}

.speech-bubble-mode-edit {
  transition: outline 0.2s ease, box-shadow 0.2s ease, z-index 0s;
}

/* ===== RESPONSIVE ===== */

@media (max-width: 768px) {
  .speech-bubble-container {
    /* Ajustements pour mobile si nécessaire */
  }
  
  .tiptap-speech-bubble-editor {
    font-size: 14px; /* Taille minimale pour mobile */
  }
}

/* ===== ACCESSIBILITÉ ===== */

.speech-bubble-container:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* ===== SÉLECTION DE TEXTE ===== */

.speech-bubble-mode-edit .tiptap-speech-bubble-editor::selection,
.speech-bubble-mode-edit .tiptap-speech-bubble-editor *::selection {
  background: rgba(59, 130, 246, 0.3);
}

/* ===== CURSEUR ===== */

.speech-bubble-mode-edit .ProseMirror {
  caret-color: currentColor;
}

/* ===== DÉBORDEMENT DE TEXTE ===== */

.speech-bubble-content {
  overflow: hidden;
}

.tiptap-speech-bubble-editor,
.bubble-text-display {
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ===== STYLES POUR DIFFÉRENTES TAILLES ===== */

.speech-bubble-container[data-size="small"] {
  min-width: 80px;
  min-height: 40px;
}

.speech-bubble-container[data-size="medium"] {
  min-width: 120px;
  min-height: 60px;
}

.speech-bubble-container[data-size="large"] {
  min-width: 200px;
  min-height: 100px;
}

/* ===== ÉTATS HOVER (MODE LECTURE SEULEMENT) ===== */

.speech-bubble-mode-read:hover {
  transform: scale(1.02);
}

.speech-bubble-mode-read:hover .speech-bubble-svg path {
  filter: brightness(1.1);
}

/* ===== DÉSACTIVATION DES INTERACTIONS PENDANT L'ÉDITION ===== */

.speech-bubble-mode-edit * {
  user-select: text;
}

.speech-bubble-mode-read *,
.speech-bubble-mode-manip * {
  user-select: none;
}
