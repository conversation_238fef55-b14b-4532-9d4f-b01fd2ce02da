'use client'

// SpeechBubbleContainer - Conteneur principal pour les Speech Bubbles TipTap
// Intègre avec le système de sélection, manipulation et modals existants

import React, { useCallback, useEffect, useMemo, useRef } from 'react'
import { DialogueElement, BubbleType } from '../types/assembly.types'
import { SpeechBubbleManager, BubbleMode } from '../core/SpeechBubbleManager'
import TipTapSpeechBubble from './TipTapSpeechBubble'
import { useCanvasContext } from '../context/CanvasContext'

interface SpeechBubbleContainerProps {
  elements: DialogueElement[]
  selectedElementIds: string[]
  onElementUpdate: (elementId: string, updates: Partial<DialogueElement>) => void
  onElementSelect: (elementId: string | null) => void
  onElementsSelect: (elementIds: string[]) => void
  onClearSelection: () => void
  className?: string
}

export default function SpeechBubbleContainer({
  elements,
  selectedElementIds,
  onElementUpdate,
  onElementSelect,
  onElementsSelect,
  onClearSelection,
  className
}: SpeechBubbleContainerProps) {
  const managerRef = useRef<SpeechBubbleManager | null>(null)
  
  // Initialisation du gestionnaire
  useEffect(() => {
    if (!managerRef.current) {
      managerRef.current = new SpeechBubbleManager({
        onUpdate: onElementUpdate,
        onSelect: onElementSelect,
        onModeChange: (elementId, mode) => {
          console.log(`🔄 Bubble mode changed: ${elementId} -> ${mode}`)
        },
        onStartEdit: (elementId) => {
          console.log(`✏️ Started editing: ${elementId}`)
          // Désactiver la sélection d'autres éléments pendant l'édition
          document.dispatchEvent(new CustomEvent('bubble-edit-start', {
            detail: { elementId }
          }))
        },
        onFinishEdit: (elementId) => {
          console.log(`✅ Finished editing: ${elementId}`)
          // Réactiver la sélection
          document.dispatchEvent(new CustomEvent('bubble-edit-end', {
            detail: { elementId }
          }))
        },
      })
    }

    // Initialiser les états des bulles existantes
    elements.forEach(element => {
      if (!managerRef.current?.getBubbleState(element.id)) {
        managerRef.current?.initializeBubble(element.id)
      }
    })

    return () => {
      managerRef.current?.cleanup()
    }
  }, [elements, onElementUpdate, onElementSelect])

  // Synchronisation avec la sélection externe
  useEffect(() => {
    if (!managerRef.current) return

    // Mettre à jour les états de sélection
    elements.forEach(element => {
      const isSelected = selectedElementIds.includes(element.id)
      const currentState = managerRef.current?.getBubbleState(element.id)
      
      if (currentState && currentState.isSelected !== isSelected) {
        managerRef.current?.updateBubbleState(element.id, { 
          isSelected,
          mode: isSelected ? 'manip' : 'read'
        })
      }
    })
  }, [selectedElementIds, elements])

  // Gestionnaires d'événements
  const handleBubbleClick = useCallback((elementId: string) => {
    managerRef.current?.handleClick(elementId)
  }, [])

  const handleBubbleDoubleClick = useCallback((elementId: string) => {
    managerRef.current?.handleDoubleClick(elementId)
  }, [])

  const handleBubbleUpdate = useCallback((elementId: string, updates: Partial<DialogueElement>) => {
    managerRef.current?.updateBubbleContent(elementId, updates)
  }, [])

  const handleModeChange = useCallback((elementId: string, mode: BubbleMode) => {
    managerRef.current?.changeBubbleMode(elementId, mode)
  }, [])

  const handleResize = useCallback((elementId: string, newTransform: Partial<DialogueElement['transform']>) => {
    onElementUpdate(elementId, { transform: { ...elements.find(e => e.id === elementId)?.transform, ...newTransform } })
  }, [elements, onElementUpdate])

  const handleRotate = useCallback((elementId: string, rotation: number) => {
    onElementUpdate(elementId, { 
      transform: { 
        ...elements.find(e => e.id === elementId)?.transform, 
        rotation 
      } 
    })
  }, [elements, onElementUpdate])

  const handleTailDrag = useCallback((elementId: string, tailDirection: number) => {
    const element = elements.find(e => e.id === elementId)
    if (!element) return

    onElementUpdate(elementId, {
      dialogueStyle: {
        ...element.dialogueStyle,
        tailAngleDegrees: tailDirection
      }
    })
  }, [elements, onElementUpdate])

  // Gestion des événements clavier
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!managerRef.current) return

      const currentEditingBubble = managerRef.current.getCurrentEditingBubble()
      
      // Échapper pour sortir du mode édition
      if (e.key === 'Escape' && currentEditingBubble) {
        managerRef.current.finishEdit(currentEditingBubble)
      }
      
      // Supprimer les bulles sélectionnées
      if (e.key === 'Delete' || e.key === 'Backspace') {
        const selectedBubbles = managerRef.current.getSelectedBubbles()
        if (selectedBubbles.length > 0 && !currentEditingBubble) {
          // Logique de suppression à implémenter
          console.log('🗑️ Delete selected bubbles:', selectedBubbles)
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  // Gestion des clics sur le canvas pour désélectionner
  useEffect(() => {
    const handleCanvasClick = (e: Event) => {
      const target = e.target as HTMLElement
      
      // Vérifier si le clic est sur une bulle
      const bubbleContainer = target.closest('[data-bubble-id]')
      if (!bubbleContainer && managerRef.current) {
        managerRef.current.clearSelection()
      }
    }

    document.addEventListener('click', handleCanvasClick)
    return () => document.removeEventListener('click', handleCanvasClick)
  }, [])

  // Rendu des bulles
  const renderedBubbles = useMemo(() => {
    return elements.map(element => {
      const bubbleState = managerRef.current?.getBubbleState(element.id)
      const isSelected = selectedElementIds.includes(element.id)
      const mode = bubbleState?.mode || 'read'

      return (
        <TipTapSpeechBubble
          key={element.id}
          element={element}
          isSelected={isSelected}
          mode={mode}
          onUpdate={handleBubbleUpdate}
          onSelect={handleBubbleClick}
          onModeChange={(newMode) => handleModeChange(element.id, newMode)}
          onResize={handleResize}
          onRotate={handleRotate}
          onTailDrag={handleTailDrag}
        />
      )
    })
  }, [
    elements, 
    selectedElementIds, 
    handleBubbleUpdate, 
    handleBubbleClick, 
    handleModeChange,
    handleResize,
    handleRotate,
    handleTailDrag
  ])

  return (
    <div 
      className={`speech-bubble-container-layer ${className || ''}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none', // Laisser passer les événements au canvas
        zIndex: 100, // Au-dessus du canvas mais sous les UI
      }}
    >
      {/* Conteneur pour les bulles avec pointer-events activés */}
      <div
        style={{
          position: 'relative',
          width: '100%',
          height: '100%',
          pointerEvents: 'auto', // Activer les événements pour les bulles
        }}
      >
        {renderedBubbles}
      </div>
    </div>
  )
}

// Hook pour utiliser le SpeechBubbleContainer avec le contexte Canvas
export function useSpeechBubbleContainer() {
  const {
    elements,
    selectedElementIds,
    updateElement,
    selectElement,
    selectElements,
    clearSelection
  } = useCanvasContext()

  // Filtrer les éléments de dialogue
  const dialogueElements = useMemo(() => 
    elements.filter((el): el is DialogueElement => 
      el.type === 'dialogue' && el.layerType === 'dialogue'
    ),
    [elements]
  )

  return {
    elements: dialogueElements,
    selectedElementIds,
    onElementUpdate: updateElement,
    onElementSelect: selectElement,
    onElementsSelect: selectElements,
    onClearSelection: clearSelection,
  }
}

// Fonction utilitaire pour créer une nouvelle bulle
export function createNewSpeechBubble(
  position: { x: number; y: number },
  bubbleType: BubbleType = 'speech'
): DialogueElement {
  const id = `bubble-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  
  return {
    type: 'dialogue',
    id,
    layerType: 'dialogue',
    text: 'Nouveau texte...',
    transform: {
      x: position.x,
      y: position.y,
      width: 200,
      height: 100,
      rotation: 0,
      alpha: 1,
      zIndex: 100,
    },
    dialogueStyle: {
      type: bubbleType,
      backgroundColor: 0xffffff,
      outlineColor: 0x000000,
      outlineWidth: 2,
      textColor: 0x000000,
      fontSize: 16,
      fontFamily: 'Arial, sans-serif',
      textAlign: 'center',
      dashedOutline: false,
      tailPosition: 'bottom-left',
      tailLength: 20,
      tailAngleDegrees: 225,
      tailAttachmentSide: 'bottom',
    },
    properties: {
      name: 'Speech Bubble',
      locked: false,
      visible: true,
    },
  }
}
