'use client'

// SpeechBubbleSelectionFrame - Cadre de sélection pour Speech Bubbles
// Intégré avec le système de sélection existant, style cohérent avec les panels

import React, { useMemo, useCallback } from 'react'
import { DialogueElement } from '../types/assembly.types'
import { BubbleMode } from './TipTapSpeechBubble'
import { cn } from '@/lib/utils'

// Types pour les handles de redimensionnement
export type HandlePosition = 
  | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  | 'top-center' | 'bottom-center' | 'middle-left' | 'middle-right'

export interface ResizeHandle {
  position: HandlePosition
  x: number
  y: number
  cursor: string
}

interface SpeechBubbleSelectionFrameProps {
  element: DialogueElement
  isSelected: boolean
  mode: BubbleMode
  onResize?: (elementId: string, newTransform: Partial<DialogueElement['transform']>) => void
  onRotate?: (elementId: string, rotation: number) => void
  onTailDrag?: (elementId: string, tailDirection: number) => void
  className?: string
}

// Configuration des handles de redimensionnement
const HANDLE_SIZE = 8
const HANDLE_OFFSET = 4

// Génération des positions des handles
const generateResizeHandles = (
  width: number, 
  height: number
): ResizeHandle[] => {
  const handles: ResizeHandle[] = []
  
  // Coins
  handles.push(
    { position: 'top-left', x: -HANDLE_OFFSET, y: -HANDLE_OFFSET, cursor: 'nw-resize' },
    { position: 'top-right', x: width + HANDLE_OFFSET, y: -HANDLE_OFFSET, cursor: 'ne-resize' },
    { position: 'bottom-left', x: -HANDLE_OFFSET, y: height + HANDLE_OFFSET, cursor: 'sw-resize' },
    { position: 'bottom-right', x: width + HANDLE_OFFSET, y: height + HANDLE_OFFSET, cursor: 'se-resize' }
  )
  
  // Centres des côtés
  handles.push(
    { position: 'top-center', x: width / 2, y: -HANDLE_OFFSET, cursor: 'n-resize' },
    { position: 'bottom-center', x: width / 2, y: height + HANDLE_OFFSET, cursor: 's-resize' },
    { position: 'middle-left', x: -HANDLE_OFFSET, y: height / 2, cursor: 'w-resize' },
    { position: 'middle-right', x: width + HANDLE_OFFSET, y: height / 2, cursor: 'e-resize' }
  )
  
  return handles
}

// Calcul de la position de la queue
const calculateTailPosition = (
  width: number,
  height: number,
  tailDirection: number,
  tailLength: number
): { x: number; y: number } => {
  const centerX = width / 2
  const centerY = height / 2
  const angle = (tailDirection * Math.PI) / 180
  
  // Point d'attache sur le bord de la bulle
  const attachX = centerX + Math.cos(angle) * (width / 2 - 10)
  const attachY = centerY + Math.sin(angle) * (height / 2 - 10)
  
  // Point de fin de la queue
  const endX = attachX + Math.cos(angle) * tailLength
  const endY = attachY + Math.sin(angle) * tailLength
  
  return { x: endX, y: endY }
}

export default function SpeechBubbleSelectionFrame({
  element,
  isSelected,
  mode,
  onResize,
  onRotate,
  onTailDrag,
  className
}: SpeechBubbleSelectionFrameProps) {
  
  // Ne pas afficher le cadre en mode édition
  if (mode === 'edit' || !isSelected) {
    return null
  }

  // Configuration du style du cadre
  const frameStyle = useMemo(() => ({
    position: 'absolute' as const,
    left: element.transform.x - 2,
    top: element.transform.y - 2,
    width: element.transform.width + 4,
    height: element.transform.height + 4,
    transform: `rotate(${element.transform.rotation}deg)`,
    pointerEvents: 'none' as const,
    zIndex: element.transform.zIndex + 1,
  }), [element.transform])

  // Génération des handles de redimensionnement
  const resizeHandles = useMemo(() => 
    generateResizeHandles(element.transform.width, element.transform.height),
    [element.transform.width, element.transform.height]
  )

  // Position de la queue pour le handle de manipulation
  const tailPosition = useMemo(() => 
    calculateTailPosition(
      element.transform.width,
      element.transform.height,
      element.dialogueStyle.tailAngleDegrees,
      element.dialogueStyle.tailLength
    ),
    [
      element.transform.width, 
      element.transform.height, 
      element.dialogueStyle.tailAngleDegrees, 
      element.dialogueStyle.tailLength
    ]
  )

  // Gestionnaires d'événements pour le redimensionnement
  const handleResizeStart = useCallback((handle: ResizeHandle, e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (!onResize) return

    const startX = e.clientX
    const startY = e.clientY
    const startWidth = element.transform.width
    const startHeight = element.transform.height
    const startX_pos = element.transform.x
    const startY_pos = element.transform.y

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX
      const deltaY = e.clientY - startY

      let newWidth = startWidth
      let newHeight = startHeight
      let newX = startX_pos
      let newY = startY_pos

      // Calcul des nouvelles dimensions selon la position du handle
      switch (handle.position) {
        case 'top-left':
          newWidth = Math.max(50, startWidth - deltaX)
          newHeight = Math.max(30, startHeight - deltaY)
          newX = startX_pos + (startWidth - newWidth)
          newY = startY_pos + (startHeight - newHeight)
          break
        case 'top-right':
          newWidth = Math.max(50, startWidth + deltaX)
          newHeight = Math.max(30, startHeight - deltaY)
          newY = startY_pos + (startHeight - newHeight)
          break
        case 'bottom-left':
          newWidth = Math.max(50, startWidth - deltaX)
          newHeight = Math.max(30, startHeight + deltaY)
          newX = startX_pos + (startWidth - newWidth)
          break
        case 'bottom-right':
          newWidth = Math.max(50, startWidth + deltaX)
          newHeight = Math.max(30, startHeight + deltaY)
          break
        case 'top-center':
          newHeight = Math.max(30, startHeight - deltaY)
          newY = startY_pos + (startHeight - newHeight)
          break
        case 'bottom-center':
          newHeight = Math.max(30, startHeight + deltaY)
          break
        case 'middle-left':
          newWidth = Math.max(50, startWidth - deltaX)
          newX = startX_pos + (startWidth - newWidth)
          break
        case 'middle-right':
          newWidth = Math.max(50, startWidth + deltaX)
          break
      }

      onResize(element.id, {
        x: newX,
        y: newY,
        width: newWidth,
        height: newHeight,
      })
    }

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [element, onResize])

  // Gestionnaire pour la manipulation de la queue
  const handleTailDrag = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (!onTailDrag) return

    const bubbleCenterX = element.transform.x + element.transform.width / 2
    const bubbleCenterY = element.transform.y + element.transform.height / 2

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - bubbleCenterX
      const deltaY = e.clientY - bubbleCenterY
      const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI)
      const normalizedAngle = ((angle % 360) + 360) % 360

      onTailDrag(element.id, normalizedAngle)
    }

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [element, onTailDrag])

  // Classes CSS
  const frameClasses = cn(
    'speech-bubble-selection-frame',
    `speech-bubble-frame-${mode}`,
    className
  )

  return (
    <div
      className={frameClasses}
      style={frameStyle}
    >
      {/* Cadre de sélection principal */}
      <div
        className="absolute inset-0 border-2 border-primary-500 border-dashed"
        style={{
          borderRadius: '8px',
          backgroundColor: 'rgba(239, 68, 68, 0.05)',
        }}
      />

      {/* Handles de redimensionnement */}
      {mode === 'manip' && resizeHandles.map((handle) => (
        <div
          key={handle.position}
          className="absolute bg-primary-500 border-2 border-white rounded-sm hover:bg-primary-600 transition-colors"
          style={{
            left: handle.x - HANDLE_SIZE / 2,
            top: handle.y - HANDLE_SIZE / 2,
            width: HANDLE_SIZE,
            height: HANDLE_SIZE,
            cursor: handle.cursor,
            pointerEvents: 'auto',
          }}
          onMouseDown={(e) => handleResizeStart(handle, e)}
        />
      ))}

      {/* Handle de manipulation de la queue */}
      {mode === 'manip' && (
        <div
          className="absolute bg-orange-500 border-2 border-white rounded-full hover:bg-orange-600 transition-colors"
          style={{
            left: tailPosition.x - 6,
            top: tailPosition.y - 6,
            width: 12,
            height: 12,
            cursor: 'crosshair',
            pointerEvents: 'auto',
          }}
          onMouseDown={handleTailDrag}
          title="Faire glisser pour ajuster la direction de la queue"
        />
      )}

      {/* Ligne de connexion vers la queue */}
      {mode === 'manip' && (
        <svg
          className="absolute inset-0 pointer-events-none"
          style={{ width: '100%', height: '100%' }}
        >
          <line
            x1={element.transform.width / 2}
            y1={element.transform.height / 2}
            x2={tailPosition.x}
            y2={tailPosition.y}
            stroke="#f59e0b"
            strokeWidth="1"
            strokeDasharray="3,3"
            opacity="0.7"
          />
        </svg>
      )}

      {/* Indicateur de rotation (optionnel) */}
      {mode === 'manip' && onRotate && (
        <div
          className="absolute bg-blue-500 border-2 border-white rounded-full hover:bg-blue-600 transition-colors"
          style={{
            left: element.transform.width / 2 - 4,
            top: -20,
            width: 8,
            height: 8,
            cursor: 'grab',
            pointerEvents: 'auto',
          }}
          title="Faire glisser pour faire pivoter"
        />
      )}
    </div>
  )
}
