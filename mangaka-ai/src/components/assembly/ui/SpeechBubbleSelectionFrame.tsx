'use client'

// SpeechBubbleSelectionFrame - Cadre de sélection pour Speech Bubbles
// Intégré avec le système de sélection existant, style cohérent avec les panels

import React, { useMemo, useCallback } from 'react'
import { DialogueElement } from '../types/assembly.types'
import { BubbleMode } from './TipTapSpeechBubble'
import { cn } from '@/lib/utils'

// Types pour les handles de redimensionnement
export type HandlePosition = 
  | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  | 'top-center' | 'bottom-center' | 'middle-left' | 'middle-right'

export interface ResizeHandle {
  position: HandlePosition
  x: number
  y: number
  cursor: string
}

interface SpeechBubbleSelectionFrameProps {
  element: DialogueElement
  isSelected: boolean
  mode: BubbleMode
  onResize?: (elementId: string, newTransform: Partial<DialogueElement['transform']>) => void
  className?: string
}

// Configuration des handles de redimensionnement
const HANDLE_SIZE = 8
const HANDLE_OFFSET = 4

// Génération des positions des handles
const generateResizeHandles = (
  width: number, 
  height: number
): ResizeHandle[] => {
  const handles: ResizeHandle[] = []
  
  // Coins
  handles.push(
    { position: 'top-left', x: -HANDLE_OFFSET, y: -HANDLE_OFFSET, cursor: 'nw-resize' },
    { position: 'top-right', x: width + HANDLE_OFFSET, y: -HANDLE_OFFSET, cursor: 'ne-resize' },
    { position: 'bottom-left', x: -HANDLE_OFFSET, y: height + HANDLE_OFFSET, cursor: 'sw-resize' },
    { position: 'bottom-right', x: width + HANDLE_OFFSET, y: height + HANDLE_OFFSET, cursor: 'se-resize' }
  )
  
  // Centres des côtés
  handles.push(
    { position: 'top-center', x: width / 2, y: -HANDLE_OFFSET, cursor: 'n-resize' },
    { position: 'bottom-center', x: width / 2, y: height + HANDLE_OFFSET, cursor: 's-resize' },
    { position: 'middle-left', x: -HANDLE_OFFSET, y: height / 2, cursor: 'w-resize' },
    { position: 'middle-right', x: width + HANDLE_OFFSET, y: height / 2, cursor: 'e-resize' }
  )
  
  return handles
}

// Calcul de la position de la queue
const calculateTailPosition = (
  width: number,
  height: number,
  tailDirection: number,
  tailLength: number
): { x: number; y: number } => {
  const centerX = width / 2
  const centerY = height / 2
  const angle = (tailDirection * Math.PI) / 180
  
  // Point d'attache sur le bord de la bulle
  const attachX = centerX + Math.cos(angle) * (width / 2 - 10)
  const attachY = centerY + Math.sin(angle) * (height / 2 - 10)
  
  // Point de fin de la queue
  const endX = attachX + Math.cos(angle) * tailLength
  const endY = attachY + Math.sin(angle) * tailLength
  
  return { x: endX, y: endY }
}

export default function SpeechBubbleSelectionFrame({
  element,
  isSelected,
  mode,
  onResize,
  className
}: SpeechBubbleSelectionFrameProps) {
  
  // Ne pas afficher le cadre en mode édition
  if (mode === 'edit' || !isSelected) {
    return null
  }

  // Configuration du style du cadre
  const frameStyle = useMemo(() => ({
    position: 'absolute' as const,
    left: element.transform.x - 2,
    top: element.transform.y - 2,
    width: element.transform.width + 4,
    height: element.transform.height + 4,
    transform: `rotate(${element.transform.rotation}deg)`,
    pointerEvents: 'none' as const,
    zIndex: element.transform.zIndex + 1,
  }), [element.transform])

  // Génération des handles de redimensionnement
  const resizeHandles = useMemo(() => 
    generateResizeHandles(element.transform.width, element.transform.height),
    [element.transform.width, element.transform.height]
  )

  // Suppression de la queue - Speech Bubbles simplifiées comme les panels

  // Gestionnaires d'événements pour le redimensionnement
  const handleResizeStart = useCallback((handle: ResizeHandle, e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (!onResize) return

    const startX = e.clientX
    const startY = e.clientY
    const startWidth = element.transform.width
    const startHeight = element.transform.height
    const startX_pos = element.transform.x
    const startY_pos = element.transform.y

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX
      const deltaY = e.clientY - startY

      let newWidth = startWidth
      let newHeight = startHeight
      let newX = startX_pos
      let newY = startY_pos

      // Calcul des nouvelles dimensions selon la position du handle
      switch (handle.position) {
        case 'top-left':
          newWidth = Math.max(50, startWidth - deltaX)
          newHeight = Math.max(30, startHeight - deltaY)
          newX = startX_pos + (startWidth - newWidth)
          newY = startY_pos + (startHeight - newHeight)
          break
        case 'top-right':
          newWidth = Math.max(50, startWidth + deltaX)
          newHeight = Math.max(30, startHeight - deltaY)
          newY = startY_pos + (startHeight - newHeight)
          break
        case 'bottom-left':
          newWidth = Math.max(50, startWidth - deltaX)
          newHeight = Math.max(30, startHeight + deltaY)
          newX = startX_pos + (startWidth - newWidth)
          break
        case 'bottom-right':
          newWidth = Math.max(50, startWidth + deltaX)
          newHeight = Math.max(30, startHeight + deltaY)
          break
        case 'top-center':
          newHeight = Math.max(30, startHeight - deltaY)
          newY = startY_pos + (startHeight - newHeight)
          break
        case 'bottom-center':
          newHeight = Math.max(30, startHeight + deltaY)
          break
        case 'middle-left':
          newWidth = Math.max(50, startWidth - deltaX)
          newX = startX_pos + (startWidth - newWidth)
          break
        case 'middle-right':
          newWidth = Math.max(50, startWidth + deltaX)
          break
      }

      onResize(element.id, {
        x: newX,
        y: newY,
        width: newWidth,
        height: newHeight,
      })
    }

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [element, onResize])

  // Suppression du gestionnaire de queue - Speech Bubbles simplifiées

  // Classes CSS
  const frameClasses = cn(
    'speech-bubble-selection-frame',
    `speech-bubble-frame-${mode}`,
    className
  )

  return (
    <div
      className={frameClasses}
      style={frameStyle}
    >
      {/* Cadre de sélection principal - Style identique aux panels */}
      <div
        className="absolute inset-0 border-2 border-blue-500"
        style={{
          borderRadius: '0px', // Pas de border-radius comme les panels
          backgroundColor: 'transparent', // Pas de fond coloré
        }}
      />

      {/* Handles de redimensionnement - Style identique aux panels */}
      {mode === 'manip' && resizeHandles.map((handle) => (
        <div
          key={handle.position}
          className="absolute bg-white border-2 border-blue-500 hover:bg-blue-50 transition-colors"
          style={{
            left: handle.x - HANDLE_SIZE / 2,
            top: handle.y - HANDLE_SIZE / 2,
            width: HANDLE_SIZE,
            height: HANDLE_SIZE,
            cursor: handle.cursor,
            pointerEvents: 'auto',
            borderRadius: '0px', // Carrés comme les panels
          }}
          onMouseDown={(e) => handleResizeStart(handle, e)}
        />
      ))}

      {/* Suppression de tous les handles de queue et rotation - Speech Bubbles simplifiées comme les panels */}
    </div>
  )
}
