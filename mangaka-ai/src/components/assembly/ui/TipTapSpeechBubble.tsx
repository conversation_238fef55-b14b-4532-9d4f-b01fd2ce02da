'use client'

// TipTapSpeechBubble - Composant Speech Bubble intelligent basé sur TipTap
// Architecture modulaire en trois couches : TipTap → Wrapper CSS/SVG → Queue 360°

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import { DialogueElement, BubbleType } from '../types/assembly.types'
import { cn } from '@/lib/utils'
import SpeechBubbleSelectionFrame from './SpeechBubbleSelectionFrame'
import './TipTapSpeechBubble.css'

// Types pour les modes UX
export type BubbleMode = 'read' | 'edit' | 'manip'

// Interface pour les props du composant
interface TipTapSpeechBubbleProps {
  element: DialogueElement
  isSelected: boolean
  mode: BubbleMode
  onUpdate: (elementId: string, updates: Partial<DialogueElement>) => void
  onSelect: (elementId: string | null) => void
  onModeChange: (mode: BubbleMode) => void
  onResize?: (elementId: string, newTransform: Partial<DialogueElement['transform']>) => void
  onRotate?: (elementId: string, rotation: number) => void
  className?: string
}

// Configuration TipTap optimisée pour speech bubbles
const createTipTapConfig = (
  content: string,
  onUpdate: (content: string) => void,
  onBlur: () => void
) => ({
  extensions: [
    StarterKit.configure({
      // Désactiver les fonctionnalités non nécessaires pour les speech bubbles
      heading: false,
      blockquote: false,
      codeBlock: false,
      horizontalRule: false,
      listItem: false,
      orderedList: false,
      bulletList: false,
    }),
    Placeholder.configure({
      placeholder: 'Tapez votre texte...',
    }),
  ],
  content: content || '',
  immediatelyRender: false,
  shouldRerenderOnTransaction: false,
  editorProps: {
    attributes: {
      class: 'tiptap-speech-bubble-editor',
      style: 'outline: none; border: none; padding: 8px; margin: 0; width: 100%; height: 100%;'
    },
  },
  onUpdate: ({ editor }: { editor: any }) => {
    const newContent = editor.getHTML()
    onUpdate(newContent)
  },
  onBlur: onBlur,
})

// Fonction pour générer le path SVG de la bulle selon le type
const generateBubblePath = (
  width: number, 
  height: number, 
  type: BubbleType,
  tailDirection: number = 0
): string => {
  const padding = 8
  const w = width - padding * 2
  const h = height - padding * 2
  
  switch (type) {
    case 'speech':
      // Bulle de dialogue classique avec coins arrondis
      const radius = Math.min(w, h) * 0.1
      return `M ${padding + radius} ${padding} 
              L ${padding + w - radius} ${padding} 
              Q ${padding + w} ${padding} ${padding + w} ${padding + radius}
              L ${padding + w} ${padding + h - radius}
              Q ${padding + w} ${padding + h} ${padding + w - radius} ${padding + h}
              L ${padding + radius} ${padding + h}
              Q ${padding} ${padding + h} ${padding} ${padding + h - radius}
              L ${padding} ${padding + radius}
              Q ${padding} ${padding} ${padding + radius} ${padding} Z`
    
    case 'thought':
      // Bulle de pensée avec forme plus organique
      return `M ${padding + w * 0.2} ${padding}
              Q ${padding + w * 0.8} ${padding} ${padding + w} ${padding + h * 0.3}
              Q ${padding + w} ${padding + h * 0.7} ${padding + w * 0.8} ${padding + h}
              Q ${padding + w * 0.2} ${padding + h} ${padding} ${padding + h * 0.7}
              Q ${padding} ${padding + h * 0.3} ${padding + w * 0.2} ${padding} Z`
    
    case 'explosion':
      // Forme irrégulière avec pointes
      const points = []
      const centerX = padding + w / 2
      const centerY = padding + h / 2
      const radiusX = w / 2
      const radiusY = h / 2
      
      for (let i = 0; i < 16; i++) {
        const angle = (i * Math.PI * 2) / 16
        const radius = i % 2 === 0 ? 0.8 : 1.2
        const x = centerX + Math.cos(angle) * radiusX * radius
        const y = centerY + Math.sin(angle) * radiusY * radius
        points.push(`${i === 0 ? 'M' : 'L'} ${x} ${y}`)
      }
      return points.join(' ') + ' Z'
    
    default:
      return generateBubblePath(width, height, 'speech', tailDirection)
  }
}

// Fonction pour générer la queue de la bulle
const generateTailPath = (
  width: number,
  height: number,
  direction: number,
  length: number = 20
): string => {
  const centerX = width / 2
  const centerY = height / 2
  const angle = (direction * Math.PI) / 180
  
  // Point d'attache sur le bord de la bulle
  const attachX = centerX + Math.cos(angle) * (width / 2 - 10)
  const attachY = centerY + Math.sin(angle) * (height / 2 - 10)
  
  // Point de fin de la queue
  const endX = attachX + Math.cos(angle) * length
  const endY = attachY + Math.sin(angle) * length
  
  // Points pour créer une queue triangulaire
  const perpAngle = angle + Math.PI / 2
  const baseWidth = 8
  const base1X = attachX + Math.cos(perpAngle) * baseWidth
  const base1Y = attachY + Math.sin(perpAngle) * baseWidth
  const base2X = attachX - Math.cos(perpAngle) * baseWidth
  const base2Y = attachY - Math.sin(perpAngle) * baseWidth
  
  return `M ${base1X} ${base1Y} L ${endX} ${endY} L ${base2X} ${base2Y} Z`
}

export default function TipTapSpeechBubble({
  element,
  isSelected,
  mode,
  onUpdate,
  onSelect,
  onModeChange,
  onResize,
  onRotate,
  className
}: TipTapSpeechBubbleProps) {
  const bubbleRef = useRef<HTMLDivElement>(null)
  const [isEditorReady, setIsEditorReady] = useState(false)
  
  // Configuration du style de la bulle
  const bubbleStyle = useMemo(() => ({
    position: 'absolute' as const,
    left: element.transform.x,
    top: element.transform.y,
    width: element.transform.width,
    height: element.transform.height,
    transform: `rotate(${element.transform.rotation}deg)`,
    opacity: element.transform.alpha,
    zIndex: element.transform.zIndex,
    cursor: mode === 'manip' ? 'move' : mode === 'read' ? 'pointer' : 'text',
  }), [element.transform, mode])

  // Configuration TipTap avec callbacks
  const handleContentUpdate = useCallback((content: string) => {
    onUpdate(element.id, { text: content })
  }, [element.id, onUpdate])

  const handleEditorBlur = useCallback(() => {
    onModeChange('read')
  }, [onModeChange])

  const editor = useEditor(
    createTipTapConfig(element.text, handleContentUpdate, handleEditorBlur),
    [element.text, mode]
  )

  // Effet pour gérer l'état de l'éditeur
  useEffect(() => {
    if (editor) {
      editor.setEditable(mode === 'edit')
      setIsEditorReady(true)
      
      if (mode === 'edit') {
        // Focus et sélection du texte en mode édition
        setTimeout(() => {
          editor.commands.focus()
          editor.commands.selectAll()
        }, 50)
      }
    }
  }, [editor, mode])

  // Gestionnaires d'événements
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    if (mode !== 'edit') {
      onSelect(element.id)
    }
  }, [element.id, mode, onSelect])

  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()
    if (mode === 'read') {
      onModeChange('edit')
    }
  }, [mode, onModeChange])

  // Génération des paths SVG
  const bubblePath = useMemo(() => 
    generateBubblePath(
      element.transform.width,
      element.transform.height,
      element.dialogueStyle.type,
      element.dialogueStyle.tailAngleDegrees
    ),
    [element.transform.width, element.transform.height, element.dialogueStyle.type, element.dialogueStyle.tailAngleDegrees]
  )

  const tailPath = useMemo(() => 
    generateTailPath(
      element.transform.width,
      element.transform.height,
      element.dialogueStyle.tailAngleDegrees,
      element.dialogueStyle.tailLength
    ),
    [element.transform.width, element.transform.height, element.dialogueStyle.tailAngleDegrees, element.dialogueStyle.tailLength]
  )

  // Classes CSS dynamiques
  const bubbleClasses = cn(
    'speech-bubble-container',
    `speech-bubble-${element.dialogueStyle.type}`,
    `speech-bubble-mode-${mode}`,
    {
      'speech-bubble-selected': isSelected,
      'speech-bubble-editing': mode === 'edit',
    },
    className
  )

  return (
    <>
      <div
        ref={bubbleRef}
        data-bubble-id={element.id}
        data-bubble-type={element.dialogueStyle.type}
        data-bubble-mode={mode}
        style={bubbleStyle}
        className={bubbleClasses}
        onClick={handleClick}
        onDoubleClick={handleDoubleClick}
      >
      {/* SVG Wrapper pour la forme de la bulle */}
      <svg
        width="100%"
        height="100%"
        className="speech-bubble-svg"
        style={{ position: 'absolute', top: 0, left: 0, pointerEvents: 'none' }}
      >
        {/* Forme principale de la bulle */}
        <path
          d={bubblePath}
          fill={`#${element.dialogueStyle.backgroundColor.toString(16).padStart(6, '0')}`}
          stroke={`#${element.dialogueStyle.outlineColor.toString(16).padStart(6, '0')}`}
          strokeWidth={element.dialogueStyle.outlineWidth}
          strokeDasharray={element.dialogueStyle.dashedOutline ? '5,5' : 'none'}
        />
        
        {/* Queue supprimée - Speech Bubbles simplifiées comme les panels */}
      </svg>

      {/* Contenu TipTap intégré */}
      <div
        className="speech-bubble-content"
        style={{
          position: 'relative',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '16px',
          fontSize: element.dialogueStyle.fontSize,
          fontFamily: element.dialogueStyle.fontFamily,
          color: `#${element.dialogueStyle.textColor.toString(16).padStart(6, '0')}`,
          textAlign: element.dialogueStyle.textAlign,
          pointerEvents: mode === 'edit' ? 'auto' : 'none',
        }}
      >
        {mode === 'edit' && editor && isEditorReady ? (
          <EditorContent 
            editor={editor}
            className="tiptap-editor-content w-full h-full flex items-center justify-center"
          />
        ) : (
          <div 
            className="bubble-text-display w-full h-full flex items-center justify-center"
            dangerouslySetInnerHTML={{ 
              __html: element.text || 'Cliquez pour éditer...' 
            }}
          />
        )}
      </div>
      </div>

      {/* Cadre de sélection intégré */}
      <SpeechBubbleSelectionFrame
        element={element}
        isSelected={isSelected}
        mode={mode}
        onResize={onResize}
      />
    </>
  )
}
