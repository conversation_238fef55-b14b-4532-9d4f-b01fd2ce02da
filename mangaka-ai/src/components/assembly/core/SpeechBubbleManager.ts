// SpeechBubbleManager - Gestionnaire centralisé pour les Speech Bubbles TipTap
// Gère les modes, la sélection, et l'intégration avec le système existant

import { DialogueElement, BubbleType } from '../types/assembly.types'

export type BubbleMode = 'read' | 'edit' | 'manip'

// Interface pour l'état d'une bulle
export interface BubbleState {
  id: string
  mode: BubbleMode
  isSelected: boolean
  isEditing: boolean
  lastModified: number
}

// Interface pour les callbacks
export interface SpeechBubbleCallbacks {
  onUpdate: (elementId: string, updates: Partial<DialogueElement>) => void
  onSelect: (elementId: string | null) => void
  onModeChange: (elementId: string, mode: BubbleMode) => void
  onStartEdit: (elementId: string) => void
  onFinishEdit: (elementId: string) => void
}

// Configuration par défaut pour les nouvelles bulles
export const DEFAULT_BUBBLE_CONFIG: Partial<DialogueElement> = {
  text: 'Nouveau texte...',
  transform: {
    x: 100,
    y: 100,
    width: 200,
    height: 100,
    rotation: 0,
    alpha: 1,
    zIndex: 100,
  },
  dialogueStyle: {
    type: 'speech' as BubbleType,
    backgroundColor: 0xffffff,
    outlineColor: 0x000000,
    outlineWidth: 2,
    textColor: 0x000000,
    fontSize: 16,
    fontFamily: 'Arial, sans-serif',
    textAlign: 'center' as const,
    dashedOutline: false,
    tailPosition: 'bottom-left' as const,
    tailLength: 20,
    tailAngleDegrees: 225,
    tailAttachmentSide: 'bottom' as const,
  },
  properties: {
    name: 'Speech Bubble',
    locked: false,
    visible: true,
  },
}

/**
 * Gestionnaire centralisé pour les Speech Bubbles
 */
export class SpeechBubbleManager {
  private bubbleStates = new Map<string, BubbleState>()
  private callbacks: SpeechBubbleCallbacks
  private currentEditingBubble: string | null = null

  constructor(callbacks: SpeechBubbleCallbacks) {
    this.callbacks = callbacks
  }

  /**
   * Initialise une nouvelle bulle
   */
  initializeBubble(elementId: string): BubbleState {
    const state: BubbleState = {
      id: elementId,
      mode: 'read',
      isSelected: false,
      isEditing: false,
      lastModified: Date.now(),
    }
    
    this.bubbleStates.set(elementId, state)
    return state
  }

  /**
   * Obtient l'état d'une bulle
   */
  getBubbleState(elementId: string): BubbleState | null {
    return this.bubbleStates.get(elementId) || null
  }

  /**
   * Met à jour l'état d'une bulle
   */
  updateBubbleState(elementId: string, updates: Partial<BubbleState>): void {
    const currentState = this.bubbleStates.get(elementId)
    if (!currentState) return

    const newState = {
      ...currentState,
      ...updates,
      lastModified: Date.now(),
    }

    this.bubbleStates.set(elementId, newState)
  }

  /**
   * Sélectionne une bulle
   */
  selectBubble(elementId: string): void {
    // Désélectionner toutes les autres bulles
    this.bubbleStates.forEach((state, id) => {
      if (id !== elementId) {
        this.updateBubbleState(id, { isSelected: false, mode: 'read' })
      }
    })

    // Sélectionner la bulle cible
    this.updateBubbleState(elementId, { 
      isSelected: true, 
      mode: 'manip' // Mode manipulation par défaut lors de la sélection
    })

    // Notifier le système de sélection existant
    this.callbacks.onSelect(elementId)
    this.callbacks.onModeChange(elementId, 'manip')
  }

  /**
   * Désélectionne toutes les bulles
   */
  clearSelection(): void {
    this.bubbleStates.forEach((state, id) => {
      this.updateBubbleState(id, { 
        isSelected: false, 
        mode: 'read',
        isEditing: false 
      })
    })

    // Arrêter l'édition si en cours
    if (this.currentEditingBubble) {
      this.finishEdit(this.currentEditingBubble)
    }

    this.callbacks.onSelect(null)
  }

  /**
   * Change le mode d'une bulle
   */
  changeBubbleMode(elementId: string, mode: BubbleMode): void {
    const currentState = this.getBubbleState(elementId)
    if (!currentState) return

    // Gestion des transitions de mode
    if (mode === 'edit') {
      // Arrêter l'édition d'autres bulles
      if (this.currentEditingBubble && this.currentEditingBubble !== elementId) {
        this.finishEdit(this.currentEditingBubble)
      }
      
      this.startEdit(elementId)
    } else if (currentState.mode === 'edit' && mode !== 'edit') {
      this.finishEdit(elementId)
    }

    this.updateBubbleState(elementId, { mode })
    this.callbacks.onModeChange(elementId, mode)
  }

  /**
   * Démarre l'édition d'une bulle
   */
  startEdit(elementId: string): void {
    // Arrêter l'édition d'autres bulles
    if (this.currentEditingBubble && this.currentEditingBubble !== elementId) {
      this.finishEdit(this.currentEditingBubble)
    }

    this.currentEditingBubble = elementId
    this.updateBubbleState(elementId, { 
      mode: 'edit', 
      isEditing: true,
      isSelected: true 
    })

    this.callbacks.onStartEdit(elementId)
    
    console.log(`✏️ Started editing bubble: ${elementId}`)
  }

  /**
   * Termine l'édition d'une bulle
   */
  finishEdit(elementId: string): void {
    if (this.currentEditingBubble === elementId) {
      this.currentEditingBubble = null
    }

    this.updateBubbleState(elementId, { 
      mode: 'read', 
      isEditing: false 
    })

    this.callbacks.onFinishEdit(elementId)
    
    console.log(`✅ Finished editing bubble: ${elementId}`)
  }

  /**
   * Gère le double-clic sur une bulle
   */
  handleDoubleClick(elementId: string): void {
    const state = this.getBubbleState(elementId)
    if (!state) return

    if (state.mode === 'read' || state.mode === 'manip') {
      this.changeBubbleMode(elementId, 'edit')
    }
  }

  /**
   * Gère le clic simple sur une bulle
   */
  handleClick(elementId: string): void {
    const state = this.getBubbleState(elementId)
    if (!state) return

    if (state.mode !== 'edit') {
      this.selectBubble(elementId)
    }
  }

  /**
   * Met à jour le contenu d'une bulle
   */
  updateBubbleContent(elementId: string, updates: Partial<DialogueElement>): void {
    this.updateBubbleState(elementId, { lastModified: Date.now() })
    this.callbacks.onUpdate(elementId, updates)
  }

  /**
   * Crée une nouvelle bulle avec configuration par défaut
   */
  createBubble(
    id: string, 
    position: { x: number; y: number },
    bubbleType: BubbleType = 'speech'
  ): DialogueElement {
    const newBubble: DialogueElement = {
      type: 'dialogue',
      id,
      layerType: 'dialogue',
      ...DEFAULT_BUBBLE_CONFIG,
      transform: {
        ...DEFAULT_BUBBLE_CONFIG.transform!,
        x: position.x,
        y: position.y,
      },
      dialogueStyle: {
        ...DEFAULT_BUBBLE_CONFIG.dialogueStyle!,
        type: bubbleType,
      },
    } as DialogueElement

    // Initialiser l'état de la bulle
    this.initializeBubble(id)
    
    return newBubble
  }

  /**
   * Supprime une bulle
   */
  removeBubble(elementId: string): void {
    if (this.currentEditingBubble === elementId) {
      this.currentEditingBubble = null
    }
    
    this.bubbleStates.delete(elementId)
  }

  /**
   * Obtient la bulle actuellement en édition
   */
  getCurrentEditingBubble(): string | null {
    return this.currentEditingBubble
  }

  /**
   * Vérifie si une bulle est en édition
   */
  isBubbleEditing(elementId: string): boolean {
    const state = this.getBubbleState(elementId)
    return state?.isEditing || false
  }

  /**
   * Obtient toutes les bulles sélectionnées
   */
  getSelectedBubbles(): string[] {
    const selected: string[] = []
    this.bubbleStates.forEach((state, id) => {
      if (state.isSelected) {
        selected.push(id)
      }
    })
    return selected
  }

  /**
   * Nettoie les ressources
   */
  cleanup(): void {
    this.bubbleStates.clear()
    this.currentEditingBubble = null
  }
}
