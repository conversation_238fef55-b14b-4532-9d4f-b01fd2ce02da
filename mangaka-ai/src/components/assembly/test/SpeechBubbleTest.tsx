'use client'

// SpeechBubbleTest - Composant de test pour les Speech Bubbles TipTap-first
// Permet de tester l'intégration sans affecter le système principal

import React, { useState } from 'react'
import { DialogueElement, BubbleType } from '../types/assembly.types'
import TipTapSpeechBubble, { BubbleMode } from '../ui/TipTapSpeechBubble'
import { createNewSpeechBubble } from '../ui/SpeechBubbleContainer'

export default function SpeechBubbleTest() {
  const [bubbles, setBubbles] = useState<DialogueElement[]>([])
  const [selectedBubbleId, setSelectedBubbleId] = useState<string | null>(null)
  const [bubbleModes, setBubbleModes] = useState<Map<string, BubbleMode>>(new Map())

  // Créer une bulle de test
  const createTestBubble = (type: BubbleType) => {
    const newBubble = createNewSpeechBubble(
      { x: Math.random() * 400 + 50, y: Math.random() * 300 + 50 },
      type
    )
    setBubbles(prev => [...prev, newBubble])
    setBubbleModes(prev => new Map(prev).set(newBubble.id, 'read'))
  }

  // Gestionnaires
  const handleBubbleUpdate = (elementId: string, updates: Partial<DialogueElement>) => {
    setBubbles(prev => prev.map(bubble => 
      bubble.id === elementId ? { ...bubble, ...updates } : bubble
    ))
  }

  const handleBubbleSelect = (elementId: string | null) => {
    setSelectedBubbleId(elementId)
  }

  const handleModeChange = (elementId: string, mode: BubbleMode) => {
    setBubbleModes(prev => new Map(prev).set(elementId, mode))
  }

  const handleResize = (elementId: string, newTransform: Partial<DialogueElement['transform']>) => {
    setBubbles(prev => prev.map(bubble => 
      bubble.id === elementId 
        ? { ...bubble, transform: { ...bubble.transform, ...newTransform } }
        : bubble
    ))
  }

  const handleRotate = (elementId: string, rotation: number) => {
    setBubbles(prev => prev.map(bubble => 
      bubble.id === elementId 
        ? { ...bubble, transform: { ...bubble.transform, rotation } }
        : bubble
    ))
  }

  const handleTailDrag = (elementId: string, tailDirection: number) => {
    setBubbles(prev => prev.map(bubble => 
      bubble.id === elementId 
        ? { 
            ...bubble, 
            dialogueStyle: { 
              ...bubble.dialogueStyle, 
              tailAngleDegrees: tailDirection 
            } 
          }
        : bubble
    ))
  }

  const clearAll = () => {
    setBubbles([])
    setBubbleModes(new Map())
    setSelectedBubbleId(null)
  }

  return (
    <div className="w-full h-screen bg-gray-100 relative">
      {/* Barre d'outils de test */}
      <div className="absolute top-4 left-4 z-50 bg-white p-4 rounded-lg shadow-lg">
        <h3 className="font-bold mb-3">Test Speech Bubbles TipTap</h3>
        
        <div className="space-y-2 mb-4">
          <button
            onClick={() => createTestBubble('speech')}
            className="block w-full px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            💬 Bulle Dialogue
          </button>
          <button
            onClick={() => createTestBubble('thought')}
            className="block w-full px-3 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            💭 Bulle Pensée
          </button>
          <button
            onClick={() => createTestBubble('explosion')}
            className="block w-full px-3 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            💥 Bulle Explosion
          </button>
        </div>

        <button
          onClick={clearAll}
          className="w-full px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          🗑️ Tout Effacer
        </button>

        <div className="mt-4 text-xs text-gray-600">
          <div>Bulles: {bubbles.length}</div>
          <div>Sélectionnée: {selectedBubbleId || 'Aucune'}</div>
        </div>
      </div>

      {/* Instructions */}
      <div className="absolute top-4 right-4 z-50 bg-white p-4 rounded-lg shadow-lg max-w-xs">
        <h4 className="font-bold mb-2">Instructions</h4>
        <ul className="text-sm space-y-1">
          <li>• <strong>Clic simple</strong> : Sélectionner</li>
          <li>• <strong>Double-clic</strong> : Éditer le texte</li>
          <li>• <strong>Handles blancs</strong> : Redimensionner</li>
          <li>• <strong>Handle orange</strong> : Direction queue</li>
          <li>• <strong>Échap</strong> : Sortir de l'édition</li>
        </ul>
      </div>

      {/* Zone de test */}
      <div className="w-full h-full relative">
        {bubbles.map(bubble => (
          <TipTapSpeechBubble
            key={bubble.id}
            element={bubble}
            isSelected={selectedBubbleId === bubble.id}
            mode={bubbleModes.get(bubble.id) || 'read'}
            onUpdate={handleBubbleUpdate}
            onSelect={handleBubbleSelect}
            onModeChange={(mode) => handleModeChange(bubble.id, mode)}
            onResize={handleResize}
            onRotate={handleRotate}
            onTailDrag={handleTailDrag}
          />
        ))}
      </div>

      {/* Grille de fond pour faciliter le positionnement */}
      <div 
        className="absolute inset-0 pointer-events-none opacity-10"
        style={{
          backgroundImage: `
            linear-gradient(to right, #000 1px, transparent 1px),
            linear-gradient(to bottom, #000 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}
      />
    </div>
  )
}
